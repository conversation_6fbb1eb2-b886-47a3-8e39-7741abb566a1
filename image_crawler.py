#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片爬虫工具
支持多种网站和场景的图片爬取
"""

import os
import re
import time
import requests
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import base64
from pathlib import Path


class ImageCrawler:
    def __init__(self, download_dir="images", delay=1):
        """
        初始化图片爬虫
        
        Args:
            download_dir: 图片下载目录
            delay: 请求间隔时间（秒）
        """
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(exist_ok=True)
        self.delay = delay
        self.session = requests.Session()
        
        # 设置请求头，模拟浏览器
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })

    def get_selenium_driver(self, headless=True):
        """获取Selenium WebDriver"""
        chrome_options = Options()
        if headless:
            chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        
        return webdriver.Chrome(options=chrome_options)

    def download_image(self, img_url, filename=None, subfolder=None):
        """
        下载单张图片
        
        Args:
            img_url: 图片URL
            filename: 保存的文件名
            subfolder: 子文件夹名称
        """
        try:
            # 处理base64图片
            if img_url.startswith('data:image'):
                return self._save_base64_image(img_url, filename, subfolder)
            
            response = self.session.get(img_url, timeout=30)
            response.raise_for_status()
            
            # 确定文件名
            if not filename:
                parsed_url = urlparse(img_url)
                filename = os.path.basename(parsed_url.path)
                if not filename or '.' not in filename:
                    # 从Content-Type推断扩展名
                    content_type = response.headers.get('content-type', '')
                    if 'jpeg' in content_type or 'jpg' in content_type:
                        ext = '.jpg'
                    elif 'png' in content_type:
                        ext = '.png'
                    elif 'gif' in content_type:
                        ext = '.gif'
                    elif 'webp' in content_type:
                        ext = '.webp'
                    else:
                        ext = '.jpg'
                    filename = f"image_{int(time.time())}{ext}"
            
            # 确定保存路径
            save_dir = self.download_dir
            if subfolder:
                save_dir = save_dir / subfolder
                save_dir.mkdir(exist_ok=True)
            
            filepath = save_dir / filename
            
            # 保存图片
            with open(filepath, 'wb') as f:
                f.write(response.content)
            
            print(f"✓ 下载成功: {filepath}")
            return str(filepath)
            
        except Exception as e:
            print(f"✗ 下载失败 {img_url}: {e}")
            return None

    def _save_base64_image(self, data_url, filename=None, subfolder=None):
        """保存base64编码的图片"""
        try:
            # 解析data URL
            header, data = data_url.split(',', 1)
            image_data = base64.b64decode(data)
            
            # 确定文件扩展名
            if 'jpeg' in header or 'jpg' in header:
                ext = '.jpg'
            elif 'png' in header:
                ext = '.png'
            elif 'gif' in header:
                ext = '.gif'
            elif 'webp' in header:
                ext = '.webp'
            else:
                ext = '.jpg'
            
            if not filename:
                filename = f"base64_image_{int(time.time())}{ext}"
            
            # 确定保存路径
            save_dir = self.download_dir
            if subfolder:
                save_dir = save_dir / subfolder
                save_dir.mkdir(exist_ok=True)
            
            filepath = save_dir / filename
            
            with open(filepath, 'wb') as f:
                f.write(image_data)
            
            print(f"✓ Base64图片保存成功: {filepath}")
            return str(filepath)
            
        except Exception as e:
            print(f"✗ Base64图片保存失败: {e}")
            return None

    def crawl_static_images(self, url, max_images=50):
        """
        爬取静态网页中的图片
        
        Args:
            url: 目标网页URL
            max_images: 最大下载图片数量
        """
        print(f"开始爬取静态图片: {url}")
        
        try:
            response = self.session.get(url)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 查找所有图片标签
            img_tags = soup.find_all('img')
            print(f"找到 {len(img_tags)} 个图片标签")
            
            downloaded = 0
            for i, img in enumerate(img_tags):
                if downloaded >= max_images:
                    break
                
                # 获取图片URL
                img_url = img.get('src') or img.get('data-src') or img.get('data-original')
                if not img_url:
                    continue
                
                # 处理相对URL
                img_url = urljoin(url, img_url)
                
                # 下载图片
                filename = f"static_{i+1:03d}_{os.path.basename(urlparse(img_url).path)}"
                if self.download_image(img_url, filename, "static"):
                    downloaded += 1
                
                time.sleep(self.delay)
            
            print(f"静态图片爬取完成，共下载 {downloaded} 张图片")
            return downloaded
            
        except Exception as e:
            print(f"静态图片爬取失败: {e}")
            return 0

    def crawl_dynamic_images(self, url, max_images=50, scroll_times=3):
        """
        爬取动态加载的图片（使用Selenium）
        
        Args:
            url: 目标网页URL
            max_images: 最大下载图片数量
            scroll_times: 页面滚动次数
        """
        print(f"开始爬取动态图片: {url}")
        
        driver = None
        try:
            driver = self.get_selenium_driver(headless=False)
            driver.get(url)
            
            # 等待页面加载
            time.sleep(3)
            
            # 滚动页面加载更多图片
            for i in range(scroll_times):
                driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)
                print(f"滚动页面 {i+1}/{scroll_times}")
            
            # 获取所有图片元素
            img_elements = driver.find_elements(By.TAG_NAME, "img")
            print(f"找到 {len(img_elements)} 个图片元素")
            
            downloaded = 0
            for i, img_element in enumerate(img_elements):
                if downloaded >= max_images:
                    break
                
                try:
                    img_url = img_element.get_attribute('src') or \
                             img_element.get_attribute('data-src') or \
                             img_element.get_attribute('data-original')
                    
                    if not img_url or img_url.startswith('data:image/svg'):
                        continue
                    
                    # 处理相对URL
                    if not img_url.startswith('http'):
                        img_url = urljoin(url, img_url)
                    
                    # 下载图片
                    filename = f"dynamic_{i+1:03d}_{int(time.time())}.jpg"
                    if self.download_image(img_url, filename, "dynamic"):
                        downloaded += 1
                    
                    time.sleep(self.delay)
                    
                except Exception as e:
                    print(f"处理图片元素失败: {e}")
                    continue
            
            print(f"动态图片爬取完成，共下载 {downloaded} 张图片")
            return downloaded
            
        except Exception as e:
            print(f"动态图片爬取失败: {e}")
            return 0
        finally:
            if driver:
                driver.quit()

    def crawl_search_images(self, keyword, search_engine="bing", max_images=30):
        """
        从搜索引擎爬取图片
        
        Args:
            keyword: 搜索关键词
            search_engine: 搜索引擎 (bing, google)
            max_images: 最大下载图片数量
        """
        print(f"开始从{search_engine}搜索图片: {keyword}")
        
        if search_engine.lower() == "bing":
            return self._crawl_bing_images(keyword, max_images)
        elif search_engine.lower() == "google":
            return self._crawl_google_images(keyword, max_images)
        else:
            print(f"不支持的搜索引擎: {search_engine}")
            return 0

    def _crawl_bing_images(self, keyword, max_images):
        """从Bing搜索图片"""
        driver = None
        try:
            driver = self.get_selenium_driver(headless=False)
            
            # 访问Bing图片搜索
            search_url = f"https://www.bing.com/images/search?q={keyword}"
            driver.get(search_url)
            time.sleep(3)
            
            # 滚动加载更多图片
            for i in range(5):
                driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)
            
            # 获取图片链接
            img_elements = driver.find_elements(By.CSS_SELECTOR, ".iusc")
            print(f"找到 {len(img_elements)} 个图片")
            
            downloaded = 0
            for i, element in enumerate(img_elements[:max_images]):
                try:
                    # 点击图片获取原图链接
                    element.click()
                    time.sleep(1)
                    
                    # 查找原图链接
                    img_element = driver.find_element(By.CSS_SELECTOR, "#mainImageWindow img")
                    img_url = img_element.get_attribute('src')
                    
                    if img_url and not img_url.startswith('data:'):
                        filename = f"bing_{keyword}_{i+1:03d}.jpg"
                        if self.download_image(img_url, filename, f"search_{keyword}"):
                            downloaded += 1
                    
                    time.sleep(self.delay)
                    
                except Exception as e:
                    print(f"处理Bing图片失败: {e}")
                    continue
            
            print(f"Bing图片搜索完成，共下载 {downloaded} 张图片")
            return downloaded
            
        except Exception as e:
            print(f"Bing图片搜索失败: {e}")
            return 0
        finally:
            if driver:
                driver.quit()


def main():
    """主函数 - 演示各种爬取场景"""
    crawler = ImageCrawler(download_dir="downloaded_images", delay=1)
    
    print("=== 图片爬虫工具 ===")
    print("1. 静态网页图片爬取")
    print("2. 动态网页图片爬取")
    print("3. 搜索引擎图片爬取")
    
    choice = input("请选择爬取方式 (1-3): ").strip()
    
    if choice == "1":
        url = input("请输入网页URL: ").strip()
        max_images = int(input("最大下载数量 (默认50): ") or "50")
        crawler.crawl_static_images(url, max_images)
        
    elif choice == "2":
        url = input("请输入网页URL: ").strip()
        max_images = int(input("最大下载数量 (默认50): ") or "50")
        scroll_times = int(input("滚动次数 (默认3): ") or "3")
        crawler.crawl_dynamic_images(url, max_images, scroll_times)
        
    elif choice == "3":
        keyword = input("请输入搜索关键词: ").strip()
        engine = input("搜索引擎 (bing/google, 默认bing): ").strip() or "bing"
        max_images = int(input("最大下载数量 (默认30): ") or "30")
        crawler.crawl_search_images(keyword, engine, max_images)
        
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
