#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级场景图片爬虫
支持多种特定场景的智能图片爬取
"""

import os
import re
import json
import time
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse, quote
from pathlib import Path
import hashlib
from PIL import Image
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed


class AdvancedScenarioCrawler:
    def __init__(self, download_dir="scenario_images", max_workers=5):
        """
        高级场景爬虫初始化
        
        Args:
            download_dir: 下载目录
            max_workers: 最大并发线程数
        """
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(exist_ok=True)
        self.max_workers = max_workers
        self.downloaded_hashes = set()  # 用于去重
        
        # 配置请求会话
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        })
        
        # 场景配置
        self.scenarios = {
            "自然风景": {
                "keywords": ["landscape", "nature", "mountain", "forest", "lake", "sunset", "sunrise"],
                "sites": [
                    "https://unsplash.com/s/photos/landscape",
                    "https://www.pexels.com/search/nature/",
                    "https://pixabay.com/images/search/landscape/"
                ],
                "filters": ["风景", "自然", "山", "湖", "森林", "日落", "日出"]
            },
            "城市建筑": {
                "keywords": ["architecture", "building", "city", "urban", "skyscraper"],
                "sites": [
                    "https://unsplash.com/s/photos/architecture",
                    "https://www.pexels.com/search/architecture/",
                    "https://pixabay.com/images/search/architecture/"
                ],
                "filters": ["建筑", "城市", "大楼", "摩天楼", "现代"]
            },
            "动物世界": {
                "keywords": ["animals", "wildlife", "pets", "cats", "dogs", "birds"],
                "sites": [
                    "https://unsplash.com/s/photos/animals",
                    "https://www.pexels.com/search/animals/",
                    "https://pixabay.com/images/search/animals/"
                ],
                "filters": ["动物", "宠物", "猫", "狗", "鸟", "野生"]
            },
            "美食料理": {
                "keywords": ["food", "cooking", "restaurant", "cuisine", "dessert"],
                "sites": [
                    "https://unsplash.com/s/photos/food",
                    "https://www.pexels.com/search/food/",
                    "https://pixabay.com/images/search/food/"
                ],
                "filters": ["美食", "料理", "餐厅", "甜品", "烹饪"]
            },
            "人物肖像": {
                "keywords": ["portrait", "people", "person", "face", "model"],
                "sites": [
                    "https://unsplash.com/s/photos/portrait",
                    "https://www.pexels.com/search/portrait/",
                    "https://pixabay.com/images/search/portrait/"
                ],
                "filters": ["人物", "肖像", "面部", "模特", "表情"]
            },
            "科技数码": {
                "keywords": ["technology", "computer", "smartphone", "gadget", "digital"],
                "sites": [
                    "https://unsplash.com/s/photos/technology",
                    "https://www.pexels.com/search/technology/",
                    "https://pixabay.com/images/search/technology/"
                ],
                "filters": ["科技", "电脑", "手机", "数码", "电子"]
            }
        }

    def get_image_hash(self, image_data):
        """获取图片数据的MD5哈希值"""
        return hashlib.md5(image_data).hexdigest()

    def is_valid_image(self, image_data, min_size=10000):
        """检查图片是否有效"""
        try:
            if len(image_data) < min_size:
                return False
            
            # 尝试打开图片验证格式
            from io import BytesIO
            Image.open(BytesIO(image_data))
            return True
        except Exception:
            return False

    def download_image_with_validation(self, img_url, save_path, filename):
        """下载并验证图片"""
        try:
            response = self.session.get(img_url, timeout=30)
            response.raise_for_status()
            
            # 验证图片
            if not self.is_valid_image(response.content):
                return False
            
            # 检查重复
            img_hash = self.get_image_hash(response.content)
            if img_hash in self.downloaded_hashes:
                return False
            
            # 保存图片
            filepath = save_path / filename
            with open(filepath, 'wb') as f:
                f.write(response.content)
            
            self.downloaded_hashes.add(img_hash)
            print(f"✓ 下载成功: {filename}")
            return True
            
        except Exception as e:
            print(f"✗ 下载失败 {img_url}: {e}")
            return False

    def extract_images_from_page(self, url, scenario_name):
        """从页面提取图片链接"""
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # 多种方式查找图片
            img_selectors = [
                'img[src]',
                'img[data-src]',
                'img[data-original]',
                '[style*="background-image"]'
            ]
            
            image_urls = []
            scenario_config = self.scenarios.get(scenario_name, {})
            keywords = scenario_config.get("keywords", [])
            filters = scenario_config.get("filters", [])
            
            for selector in img_selectors:
                elements = soup.select(selector)
                
                for element in elements:
                    img_url = None
                    
                    if element.name == 'img':
                        img_url = element.get('src') or element.get('data-src') or element.get('data-original')
                    else:
                        # 处理背景图片
                        style = element.get('style', '')
                        match = re.search(r'background-image:\s*url\(["\']?([^"\']+)["\']?\)', style)
                        if match:
                            img_url = match.group(1)
                    
                    if not img_url:
                        continue
                    
                    # 处理相对URL
                    img_url = urljoin(url, img_url)
                    
                    # 过滤小图标和无关图片
                    if any(skip in img_url.lower() for skip in ['icon', 'logo', 'avatar', 'thumb']):
                        continue
                    
                    # 检查是否符合场景
                    img_text = (element.get('alt', '') + ' ' + element.get('title', '')).lower()
                    if keywords and not any(keyword in img_text for keyword in keywords):
                        # 如果没有匹配的关键词，检查中文过滤词
                        if filters and not any(filter_word in img_text for filter_word in filters):
                            continue
                    
                    image_urls.append(img_url)
            
            return list(set(image_urls))  # 去重
            
        except Exception as e:
            print(f"页面解析失败 {url}: {e}")
            return []

    def crawl_scenario_images(self, scenario_name, max_images=50, max_pages=3):
        """
        爬取特定场景的图片
        
        Args:
            scenario_name: 场景名称
            max_images: 最大下载图片数
            max_pages: 最大爬取页面数
        """
        if scenario_name not in self.scenarios:
            print(f"不支持的场景: {scenario_name}")
            return 0
        
        print(f"开始爬取场景: {scenario_name}")
        
        # 创建场景目录
        scenario_dir = self.download_dir / scenario_name
        scenario_dir.mkdir(exist_ok=True)
        
        scenario_config = self.scenarios[scenario_name]
        sites = scenario_config["sites"]
        
        all_image_urls = []
        
        # 从各个网站收集图片链接
        for site in sites[:max_pages]:
            print(f"正在解析网站: {site}")
            image_urls = self.extract_images_from_page(site, scenario_name)
            all_image_urls.extend(image_urls)
            time.sleep(2)  # 避免请求过快
        
        # 去重并限制数量
        unique_urls = list(set(all_image_urls))[:max_images]
        print(f"找到 {len(unique_urls)} 个图片链接")
        
        # 并发下载图片
        downloaded_count = 0
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_url = {}
            
            for i, img_url in enumerate(unique_urls):
                filename = f"{scenario_name}_{i+1:03d}_{int(time.time())}.jpg"
                future = executor.submit(
                    self.download_image_with_validation,
                    img_url, scenario_dir, filename
                )
                future_to_url[future] = img_url
            
            for future in as_completed(future_to_url):
                if future.result():
                    downloaded_count += 1
                
                # 添加延迟避免过快请求
                time.sleep(0.5)
        
        print(f"场景 {scenario_name} 爬取完成，共下载 {downloaded_count} 张图片")
        return downloaded_count

    def crawl_all_scenarios(self, max_images_per_scenario=30):
        """爬取所有场景的图片"""
        total_downloaded = 0
        
        for scenario_name in self.scenarios.keys():
            print(f"\n{'='*50}")
            downloaded = self.crawl_scenario_images(scenario_name, max_images_per_scenario)
            total_downloaded += downloaded
            time.sleep(5)  # 场景间休息
        
        print(f"\n所有场景爬取完成，总共下载 {total_downloaded} 张图片")
        return total_downloaded

    def search_and_crawl(self, custom_keyword, max_images=30):
        """
        根据自定义关键词搜索并爬取图片
        
        Args:
            custom_keyword: 自定义搜索关键词
            max_images: 最大下载数量
        """
        print(f"开始搜索关键词: {custom_keyword}")
        
        # 创建关键词目录
        keyword_dir = self.download_dir / f"search_{custom_keyword}"
        keyword_dir.mkdir(exist_ok=True)
        
        # 构建搜索URL
        search_sites = [
            f"https://unsplash.com/s/photos/{quote(custom_keyword)}",
            f"https://www.pexels.com/search/{quote(custom_keyword)}/",
            f"https://pixabay.com/images/search/{quote(custom_keyword)}/"
        ]
        
        all_image_urls = []
        
        for site in search_sites:
            print(f"搜索网站: {site}")
            # 这里可以添加特定的搜索页面解析逻辑
            image_urls = self.extract_images_from_page(site, "自定义搜索")
            all_image_urls.extend(image_urls)
            time.sleep(2)
        
        # 下载图片
        unique_urls = list(set(all_image_urls))[:max_images]
        downloaded_count = 0
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = []
            
            for i, img_url in enumerate(unique_urls):
                filename = f"{custom_keyword}_{i+1:03d}.jpg"
                future = executor.submit(
                    self.download_image_with_validation,
                    img_url, keyword_dir, filename
                )
                futures.append(future)
            
            for future in as_completed(futures):
                if future.result():
                    downloaded_count += 1
                time.sleep(0.5)
        
        print(f"关键词 {custom_keyword} 搜索完成，共下载 {downloaded_count} 张图片")
        return downloaded_count

    def generate_report(self):
        """生成爬取报告"""
        report = {
            "总下载目录": str(self.download_dir),
            "场景统计": {},
            "总图片数": 0
        }
        
        for scenario_dir in self.download_dir.iterdir():
            if scenario_dir.is_dir():
                image_count = len(list(scenario_dir.glob("*.jpg"))) + len(list(scenario_dir.glob("*.png")))
                report["场景统计"][scenario_dir.name] = image_count
                report["总图片数"] += image_count
        
        # 保存报告
        report_file = self.download_dir / "crawl_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n爬取报告已保存到: {report_file}")
        print(f"总共下载图片: {report['总图片数']} 张")
        
        return report


def main():
    """主函数"""
    crawler = AdvancedScenarioCrawler(download_dir="advanced_images", max_workers=3)
    
    print("=== 高级场景图片爬虫 ===")
    print("1. 爬取单个场景")
    print("2. 爬取所有场景")
    print("3. 自定义关键词搜索")
    print("4. 生成爬取报告")
    
    choice = input("请选择操作 (1-4): ").strip()
    
    if choice == "1":
        print("\n可用场景:")
        for i, scenario in enumerate(crawler.scenarios.keys(), 1):
            print(f"{i}. {scenario}")
        
        scenario_choice = input("请选择场景编号: ").strip()
        if scenario_choice.isdigit():
            scenarios = list(crawler.scenarios.keys())
            if 1 <= int(scenario_choice) <= len(scenarios):
                selected_scenario = scenarios[int(scenario_choice) - 1]
                max_images = int(input("最大下载数量 (默认50): ") or "50")
                crawler.crawl_scenario_images(selected_scenario, max_images)
    
    elif choice == "2":
        max_per_scenario = int(input("每个场景最大下载数量 (默认30): ") or "30")
        crawler.crawl_all_scenarios(max_per_scenario)
    
    elif choice == "3":
        keyword = input("请输入搜索关键词: ").strip()
        max_images = int(input("最大下载数量 (默认30): ") or "30")
        crawler.search_and_crawl(keyword, max_images)
    
    elif choice == "4":
        crawler.generate_report()
    
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
