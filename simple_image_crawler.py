#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单图片爬虫示例
适用于快速爬取网页图片
"""

import os
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
import time
from pathlib import Path


def download_image(img_url, save_dir, filename=None):
    """下载单张图片"""
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(img_url, headers=headers, timeout=30)
        response.raise_for_status()
        
        # 确定文件名
        if not filename:
            parsed_url = urlparse(img_url)
            filename = os.path.basename(parsed_url.path)
            if not filename or '.' not in filename:
                filename = f"image_{int(time.time())}.jpg"
        
        # 保存图片
        filepath = save_dir / filename
        with open(filepath, 'wb') as f:
            f.write(response.content)
        
        print(f"✓ 下载成功: {filename}")
        return True
        
    except Exception as e:
        print(f"✗ 下载失败 {img_url}: {e}")
        return False


def crawl_website_images(url, save_dir="images", max_images=20):
    """
    爬取网站图片
    
    Args:
        url: 目标网站URL
        save_dir: 保存目录
        max_images: 最大下载数量
    """
    # 创建保存目录
    save_path = Path(save_dir)
    save_path.mkdir(exist_ok=True)
    
    print(f"开始爬取网站图片: {url}")
    
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # 查找所有图片标签
        img_tags = soup.find_all('img')
        print(f"找到 {len(img_tags)} 个图片标签")
        
        downloaded = 0
        for i, img in enumerate(img_tags):
            if downloaded >= max_images:
                break
            
            # 获取图片URL
            img_url = img.get('src') or img.get('data-src') or img.get('data-original')
            if not img_url:
                continue
            
            # 跳过小图标和base64图片
            if any(skip in img_url.lower() for skip in ['icon', 'logo', 'avatar', 'data:image']):
                continue
            
            # 处理相对URL
            img_url = urljoin(url, img_url)
            
            # 下载图片
            filename = f"img_{i+1:03d}_{os.path.basename(urlparse(img_url).path)}"
            if download_image(img_url, save_path, filename):
                downloaded += 1
            
            time.sleep(1)  # 避免请求过快
        
        print(f"爬取完成，共下载 {downloaded} 张图片到 {save_path}")
        
    except Exception as e:
        print(f"爬取失败: {e}")


def crawl_specific_scenarios():
    """爬取特定场景的图片示例"""
    
    scenarios = {
        "风景图片": [
            "https://www.nationalgeographic.com/photography/",
            "https://unsplash.com/s/photos/landscape"
        ],
        "动物图片": [
            "https://www.nationalgeographic.com/animals/",
            "https://unsplash.com/s/photos/animals"
        ],
        "建筑图片": [
            "https://unsplash.com/s/photos/architecture",
            "https://www.archdaily.com/"
        ],
        "美食图片": [
            "https://unsplash.com/s/photos/food",
            "https://www.foodnetwork.com/recipes"
        ]
    }
    
    print("=== 特定场景图片爬取 ===")
    for i, scenario in enumerate(scenarios.keys(), 1):
        print(f"{i}. {scenario}")
    
    choice = input("请选择场景 (1-4): ").strip()
    scenario_list = list(scenarios.keys())
    
    if choice.isdigit() and 1 <= int(choice) <= len(scenario_list):
        selected_scenario = scenario_list[int(choice) - 1]
        urls = scenarios[selected_scenario]
        
        print(f"开始爬取 {selected_scenario}")
        
        for url in urls:
            print(f"\n正在处理: {url}")
            save_dir = f"images/{selected_scenario.replace('图片', '')}"
            crawl_website_images(url, save_dir, max_images=10)
    else:
        print("无效选择")


def main():
    """主函数"""
    print("=== 简单图片爬虫 ===")
    print("1. 爬取指定网站图片")
    print("2. 爬取特定场景图片")
    
    choice = input("请选择模式 (1-2): ").strip()
    
    if choice == "1":
        url = input("请输入网站URL: ").strip()
        max_images = int(input("最大下载数量 (默认20): ") or "20")
        save_dir = input("保存目录 (默认images): ").strip() or "images"
        crawl_website_images(url, save_dir, max_images)
        
    elif choice == "2":
        crawl_specific_scenarios()
        
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
