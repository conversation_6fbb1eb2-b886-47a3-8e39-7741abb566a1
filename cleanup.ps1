# 定期清理脚本
Write-Host "开始清理 C 盘..." -ForegroundColor Green

# 清理临时文件
Remove-Item -Path "$env:TEMP\*" -Recurse -Force -ErrorAction SilentlyContinue
Remove-Item -Path "C:\Windows\Temp\*" -Recurse -Force -ErrorAction SilentlyContinue

# 清理 Rust 构建缓存
if (Test-Path "target") {
    Remove-Item -Path "target\debug" -Recurse -Force -ErrorAction SilentlyContinue
}

# 清理回收站
Clear-RecycleBin -Force -ErrorAction SilentlyContinue

# 显示清理后的空间
$disk = Get-WmiObject -Class Win32_LogicalDisk | Where-Object {$_.DeviceID -eq "C:"}
$freeGB = [math]::Round($disk.FreeSpace / 1GB, 2)
Write-Host "C 盘剩余空间: $freeGB GB" -ForegroundColor Yellow