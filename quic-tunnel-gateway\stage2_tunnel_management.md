# 阶段二：隧道管理和连接池

## 任务目标
- 实现隧道的创建、管理和销毁
- 支持多个并发隧道
- 实现连接池和资源管理

## 实现步骤

### 1. 扩展协议定义
更新 `src/common/protocol.rs`：
- 添加 `TunnelCreated`、`TunnelStatus`、`TunnelStatusResponse`、`Heartbeat` 消息
- 定义 `TunnelState` 结构体（状态、统计信息、活跃流数量）
- 添加 `TunnelStatus` 枚举：Connecting、Active、Idle、Error、Closed
- 扩展 `ControlCommand` 支持 HealthCheck

### 2. 实现隧道管理器
在 `src/server/tunnel_manager.rs` 中实现：
- `TunnelInfo` 结构体存储隧道详细信息
- `StreamInfo` 结构体管理流状态
- `TunnelManager` 管理所有活跃隧道
- 隧道创建、查找、删除接口
- 客户端到隧道的映射关系
- 活动时间更新和空闲隧道清理
- 流级别的管理（添加、删除、统计）

### 3. 实现连接池
在 `src/server/connection_pool.rs` 中实现：
- `PooledConnection` 结构体（连接、创建时间、使用次数）
- 按目标地址分组的连接池
- 连接复用和负载均衡
- 连接健康检查和过期清理
- 最大连接数限制和信号量控制
- 连接统计和监控接口

### 4. 增强 QUIC 服务端
更新 `src/server/quic_server.rs`：
- 集成 `TunnelManager` 和 `ConnectionPool`
- 支持并发处理多个隧道请求
- 实现隧道到目标服务的连接建立验证
- 添加定时清理任务（隧道和连接池）
- 完善错误处理和资源清理
- 支持隧道状态查询和控制命令

### 5. 增强 QUIC 客户端
更新 `src/client/quic_client.rs`：
- 支持创建多个隧道
- 隧道状态管理和查询
- 自动心跳机制
- 隧道关闭和清理
- 连接异常时的自动重连

### 6. 实现监控和指标
- 隧道数量和状态统计
- 连接池使用情况监控
- 性能指标收集（字节传输、请求计数）
- 错误统计和健康检查

## 验收标准
- [x] 支持同时创建多个隧道到不同目标
- [x] 隧道能正确路由到指定的目标服务
- [x] 连接异常时能自动清理资源
- [x] 监控指标准确反映系统状态
- [x] 连接池正常工作，有效复用连接
- [x] 心跳机制保持连接活跃
- [x] 空闲隧道自动清理

## 测试场景
```powershell
# 启动多个测试服务
python -m http.server 8080
python -m http.server 8081

# 测试多隧道创建和管理
cargo run --bin client
```

## 下一阶段准备
- 理解 HTTP 协议解析和代理
- 准备集成 Web 框架（Axum）
- 学习 HTTP 请求路由和负载均衡