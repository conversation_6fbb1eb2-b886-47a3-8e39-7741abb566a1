# 阶段三：HTTP 协议支持和网关功能

## 任务目标
- 实现 HTTP 请求的完整代理功能
- 支持 HTTP/1.1 和 HTTP/2
- 实现请求路由和负载均衡

## 实现步骤

### 1. 添加 HTTP 相关依赖
在 `Cargo.toml` 中添加：
- axum = "0.7" (Web 框架)
- hyper = { version = "1.0", features = ["full"] }
- tower = { version = "0.4", features = ["full"] }
- tower-http = { version = "0.5", features = ["cors", "trace"] }
- http = "1.0"
- http-body-util = "0.1"
- bytes = "1.0"
- futures-util = "0.3"
- serde_json = "1.0"
- serde_yaml = "0.9"

### 2. 实现 HTTP 解析器
在 `src/common/http_parser.rs` 中实现：
- `HttpRequest` 和 `HttpResponse` 结构体
- Hyper Request/Response 与自定义结构的转换
- 原始 HTTP 文本解析和生成
- 流式 Body 处理
- 错误响应生成工具

### 3. 扩展隧道协议
更新 `src/common/protocol.rs`：
- 添加 `HttpRequest` 和 `HttpResponse` 消息类型
- 支持请求 ID 用于请求-响应匹配
- 扩展 `TunnelState` 包含 HTTP 特定统计
- 添加请求计数和错误计数字段

### 4. 实现 HTTP 网关
在 `src/gateway/http_gateway.rs` 中实现：
- Axum 路由配置和中间件
- HTTP 请求拦截和预处理
- 通过 QUIC 隧道转发 HTTP 请求
- 响应处理和返回给客户端
- 支持各种 HTTP 方法（GET、POST、PUT、DELETE）
- Header 和 Body 完整传输

### 5. 实现请求路由
在 `src/gateway/router.rs` 中实现：
- 基于路径的路由规则配置
- 基于 Header 的路由（Host、User-Agent 等）
- 负载均衡策略（轮询、权重、最少连接）
- 健康检查集成
- 路由规则热重载

### 6. 添加中间件支持
- 请求日志记录和追踪
- 认证和授权中间件
- 限流和熔断保护
- CORS 支持
- 请求/响应修改中间件

### 7. 实现网关服务器
在 `src/gateway/main.rs` 中实现：
- 集成 HTTP 网关和 QUIC 客户端
- 配置文件加载（YAML/JSON）
- 优雅关闭和信号处理
- 健康检查端点
- 指标导出端点

## 验收标准
- [x] HTTP 请求能正确通过 QUIC 隧道转发
- [x] 支持所有标准 HTTP 方法
- [x] Header 和 Body 完整无损传输
- [x] 错误处理完善，返回正确的 HTTP 状态码
- [x] 支持并发 HTTP 请求处理
- [x] 路由规则正确匹配和转发
- [x] 负载均衡功能正常工作

## 配置示例
```yaml
# gateway.yaml
server:
  bind: "0.0.0.0:8080"
  
quic:
  server_addr: "127.0.0.1:4433"
  
routes:
  - path: "/api/*"
    target: "backend-api"
    load_balance: "round_robin"
  - host: "admin.example.com"
    target: "admin-service"
    
targets:
  backend-api:
    - "127.0.0.1:8081"
    - "127.0.0.1:8082"
  admin-service:
    - "127.0.0.1:8083"
```

## 测试场景
```powershell
# 启动后端服务
python -m http.server 8081

# 启动网关
cargo run --bin gateway

# 测试 HTTP 请求
curl -X GET http://localhost:8080/api/test
curl -X POST http://localhost:8080/api/data -d '{"key":"value"}'
```

## 下一阶段准备
- 理解 WebSocket 协议和升级机制
- 学习流式数据处理和优化
- 准备实现双向实时通信