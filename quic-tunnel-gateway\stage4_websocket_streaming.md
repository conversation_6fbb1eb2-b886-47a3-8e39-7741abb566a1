# 阶段四：WebSocket 和流式协议支持

## 任务目标
- 实现 WebSocket 协议支持
- 支持双向实时通信
- 实现流式数据传输优化

## 实现步骤

### 1. 添加 WebSocket 依赖
在 `Cargo.toml` 中添加：
- tokio-tungstenite = "0.20" (WebSocket 实现)
- futures-util = "0.3"
- tungstenite = "0.20"

### 2. 实现 WebSocket 处理器
在 `src/gateway/websocket_handler.rs` 中实现：
- WebSocket 握手处理和协议升级
- WebSocket 消息帧解析和构建
- 双向消息转发（客户端 ↔ QUIC 隧道 ↔ 后端）
- WebSocket 连接状态管理
- 支持文本和二进制消息
- Ping/Pong 心跳处理

### 3. 扩展隧道协议
更新 `src/common/protocol.rs`：
- 添加 `WebSocketMessage` 类型
- 支持 WebSocket 帧类型（Text、Binary、Ping、Pong、Close）
- 添加 WebSocket 特定的控制命令
- 扩展流 ID 管理支持 WebSocket 连接

### 4. 实现流式数据优化
在 `src/common/stream_optimizer.rs` 中实现：
- 数据分片和重组算法
- 流控制和背压处理
- 优先级队列管理
- 数据压缩和解压缩
- 缓冲区管理和内存优化

### 5. 集成到网关
更新 `src/gateway/http_gateway.rs`：
- WebSocket 升级请求检测
- WebSocket 路由规则支持
- 与 HTTP 网关的无缝集成
- WebSocket 连接池管理

### 6. 实现连接管理
在 `src/gateway/websocket_manager.rs` 中实现：
- WebSocket 连接池和复用
- 连接超时和心跳处理
- 优雅关闭和错误恢复
- 连接状态监控和统计

### 7. 支持其他流式协议
- gRPC 流式调用支持
- Server-Sent Events (SSE) 支持
- 自定义流式协议扩展接口

## 验收标准
- [x] WebSocket 连接能正常建立和维持
- [x] 双向消息传输正常无丢失
- [x] 支持大量并发 WebSocket 连接（1000+）
- [x] 连接异常时能正确处理和恢复
- [x] 心跳机制保持连接活跃
- [x] 流式数据传输优化有效
- [x] 内存使用合理，无内存泄漏

## WebSocket 路由配置
```yaml
routes:
  - path: "/ws/*"
    protocol: "websocket"
    target: "websocket-backend"
    options:
      heartbeat_interval: 30
      max_message_size: 1048576
      compression: true
```

## 测试场景
```powershell
# 启动 WebSocket 测试服务器
node websocket-server.js

# 启动网关
cargo run --bin gateway

# 测试 WebSocket 连接
wscat -c ws://localhost:8080/ws/test
```

## 性能测试
- 并发连接数测试
- 消息吞吐量测试
- 延迟测试
- 内存使用测试

## 下一阶段准备
- 学习性能监控和指标收集
- 准备生产环境部署配置
- 了解容器化和服务发现