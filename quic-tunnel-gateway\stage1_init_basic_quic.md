# 阶段一：项目初始化和基础 QUIC 通信

## 任务目标
- 初始化 Rust 项目结构
- 实现基础的 QUIC 服务端和客户端
- 建立基本的双向通信

## 实现步骤

### 1. 创建项目结构
```powershell
# 创建主项目
cargo new quic-tunnel-gateway --bin
cd quic-tunnel-gateway

# 创建子模块目录
mkdir src\server, src\client, src\common

# 创建模块文件
New-Item src\server\mod.rs, src\client\mod.rs, src\common\mod.rs -ItemType File
```

### 2. 配置依赖
在 `Cargo.toml` 中添加：
- quinn = "0.10" (QUIC 实现)
- rustls = "0.21" (TLS 支持)
- tokio = { version = "1.0", features = ["full"] }
- rcgen = "0.11" (证书生成)
- serde = { version = "1.0", features = ["derive"] }
- bincode = "1.3" (二进制序列化)
- tracing = "0.1" (日志)
- anyhow = "1.0" (错误处理)
- uuid = { version = "1.0", features = ["v4"] }

### 3. 实现通用协议定义
在 `src/common/protocol.rs` 中定义：
- `TunnelMessage` 枚举：CreateTunnel、Data、Control、Response
- `ControlCommand` 枚举：Close、Ping、Pong、StreamReset
- 支持 serde 序列化/反序列化

### 4. 实现基础 QUIC 服务端
在 `src/server/quic_server.rs` 中实现：
- 自签名证书生成（rcgen）
- QUIC 端点配置（最大并发流、空闲超时）
- 连接接受和处理循环
- 双向流处理
- Echo 功能测试消息处理

### 5. 实现基础 QUIC 客户端
在 `src/client/quic_client.rs` 中实现：
- 客户端配置（跳过证书验证用于测试）
- 连接建立到服务器
- 发送测试消息
- 接收和解析响应
- 证书验证跳过实现（仅测试用）

### 6. 创建可执行文件
- `src/server/main.rs`：启动 QUIC 服务器
- `src/client/main.rs`：连接并发送测试消息
- 配置 `[[bin]]` 节点支持多个可执行文件

## 验收标准
- [x] 项目结构清晰，模块划分合理
- [x] QUIC 服务端能正常启动并监听 127.0.0.1:4433
- [x] 客户端能成功连接并发送消息
- [x] 服务端能接收消息并回复 Echo
- [x] 日志输出清晰，便于调试
- [x] 支持并发连接处理

## 测试命令
```powershell
# 启动服务端
cargo run --bin server

# 运行客户端测试
cargo run --bin client
```

## 下一阶段准备
- 理解 QUIC 连接和流的概念
- 熟悉异步编程和错误处理
- 准备实现更复杂的隧道管理逻辑