# 图片爬虫工具

这是一个功能完整的Python图片爬虫工具，支持多种场景的图片爬取。

## 功能特点

- 🖼️ **多种爬取方式**：静态网页、动态网页、搜索引擎
- 🎯 **场景化爬取**：风景、动物、建筑、美食等特定场景
- 🔄 **智能处理**：支持相对URL、base64图片、动态加载
- 📁 **自动分类**：按场景和来源自动创建文件夹
- ⚡ **高效稳定**：请求间隔控制、错误重试机制

## 安装依赖

```bash
pip install -r requirements.txt
```

### 额外要求

- **Chrome浏览器**：用于动态网页爬取
- **ChromeDriver**：需要下载并配置到PATH中

## 使用方法

### 1. 完整功能版本

```bash
python image_crawler.py
```

支持以下功能：
- 静态网页图片爬取
- 动态网页图片爬取（使用Selenium）
- 搜索引擎图片爬取（Bing/Google）

### 2. 简化版本

```bash
python simple_image_crawler.py
```

适合快速爬取，包含：
- 指定网站图片爬取
- 特定场景图片爬取

## 代码示例

### 基本使用

```python
from image_crawler import ImageCrawler

# 创建爬虫实例
crawler = ImageCrawler(download_dir="my_images", delay=1)

# 爬取静态网页图片
crawler.crawl_static_images("https://example.com", max_images=30)

# 爬取动态网页图片
crawler.crawl_dynamic_images("https://example.com", max_images=30, scroll_times=5)

# 搜索引擎图片爬取
crawler.crawl_search_images("风景", search_engine="bing", max_images=20)
```

### 自定义爬取

```python
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin

def custom_crawl(url, keyword):
    """自定义爬取函数"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    response = requests.get(url, headers=headers)
    soup = BeautifulSoup(response.content, 'html.parser')
    
    # 查找包含关键词的图片
    img_tags = soup.find_all('img')
    for img in img_tags:
        alt_text = img.get('alt', '').lower()
        if keyword.lower() in alt_text:
            img_url = urljoin(url, img.get('src'))
            # 下载图片...
```

## 支持的场景

### 1. 风景图片
- 自然风光
- 城市景观
- 日出日落
- 山川河流

### 2. 动物图片
- 野生动物
- 宠物
- 海洋生物
- 鸟类

### 3. 建筑图片
- 现代建筑
- 古典建筑
- 室内设计
- 城市规划

### 4. 美食图片
- 中式料理
- 西式料理
- 甜品饮品
- 食材原料

## 配置选项

### 基本配置

```python
crawler = ImageCrawler(
    download_dir="images",    # 下载目录
    delay=1                   # 请求间隔（秒）
)
```

### 高级配置

```python
# 自定义请求头
crawler.session.headers.update({
    'Referer': 'https://example.com',
    'Cookie': 'your_cookie_here'
})

# 设置代理
crawler.session.proxies = {
    'http': 'http://proxy:port',
    'https': 'https://proxy:port'
}
```

## 注意事项

### 法律合规
- ⚖️ 遵守网站robots.txt规则
- 📋 尊重版权和使用条款
- 🚫 不要用于商业用途（除非获得授权）
- ⏱️ 控制爬取频率，避免对服务器造成压力

### 技术建议
- 🔄 设置合理的请求间隔
- 🛡️ 使用代理IP避免被封
- 📊 监控爬取状态和错误
- 💾 定期清理下载的图片

### 常见问题

**Q: 为什么有些图片下载失败？**
A: 可能是图片链接失效、需要登录、或者有反爬机制。

**Q: 如何处理动态加载的图片？**
A: 使用Selenium模拟浏览器行为，或者分析网页的Ajax请求。

**Q: 如何避免下载重复图片？**
A: 可以通过文件MD5校验或图片相似度比较来去重。

## 扩展功能

### 1. 图片去重

```python
import hashlib
from PIL import Image

def get_image_hash(filepath):
    """获取图片MD5哈希值"""
    with open(filepath, 'rb') as f:
        return hashlib.md5(f.read()).hexdigest()

def remove_duplicates(image_dir):
    """删除重复图片"""
    seen_hashes = set()
    for filepath in Path(image_dir).glob('*.jpg'):
        img_hash = get_image_hash(filepath)
        if img_hash in seen_hashes:
            filepath.unlink()  # 删除重复文件
        else:
            seen_hashes.add(img_hash)
```

### 2. 图片质量过滤

```python
from PIL import Image

def filter_by_size(image_dir, min_width=200, min_height=200):
    """按尺寸过滤图片"""
    for filepath in Path(image_dir).glob('*.jpg'):
        try:
            with Image.open(filepath) as img:
                if img.width < min_width or img.height < min_height:
                    filepath.unlink()  # 删除小尺寸图片
        except Exception:
            filepath.unlink()  # 删除损坏的图片
```

### 3. 批量处理

```python
def batch_crawl(urls, keywords):
    """批量爬取多个网站"""
    crawler = ImageCrawler()
    
    for url in urls:
        for keyword in keywords:
            print(f"爬取 {url} 中的 {keyword} 图片")
            # 实现具体的爬取逻辑
```

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和网站使用条款。
